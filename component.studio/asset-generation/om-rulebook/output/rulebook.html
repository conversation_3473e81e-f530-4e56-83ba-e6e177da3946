<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Om: The Journey Rulebook - Om: The Journey Rulebook</title>
    <link rel="stylesheet" href="om.css">
    <style>
        /* Fallback styles in case external resources don't load */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
        }

        /* Responsive navigation */
        .rulebook-nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background-color: #205F80;
            color: white;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 1rem;
        }

        .nav-title {
            font-weight: bold;
            margin: 0;
        }

        .menu-button {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-links li {
            margin-left: 1.5rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
        }

        .nav-links a:hover {
            text-decoration: underline;
        }

        /* Content container */
        .rulebook-container {
            max-width: 1200px;
            margin: 80px auto 2rem;
            padding: 0 1rem;
            overflow-x: hidden;
            word-wrap: break-word;
        }

        /* Rulebook sections */
        .rulebook-section {
            margin-bottom: 3rem;
            padding-top: 1rem;
        }

        .section-title {
            color: #205F80;
            border-bottom: 2px solid #E87E1E;
            padding-bottom: 0.5rem;
        }

        /* Footer */
        .rulebook-footer {
            background-color: #F4EBDD;
            padding: 2rem 1rem;
            margin-top: 3rem;
            border-top: 4px solid #E87E1E;
        }

        .icon-legend {
            max-width: 1200px;
            margin: 0 auto;
        }

        .back-to-top {
            display: block;
            text-align: center;
            margin-top: 2rem;
            color: #205F80;
            text-decoration: none;
            font-weight: bold;
        }

        /* Additional responsive fixes */
        * {
            box-sizing: border-box;
        }

        body {
            overflow-x: hidden;
        }

        table {
            width: 100%;
            table-layout: fixed;
            word-wrap: break-word;
        }

        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-x: auto;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .menu-button {
                display: block;
            }

            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background-color: #205F80;
                flex-direction: column;
                padding: 1rem 0;
            }

            .nav-links.show {
                display: flex;
            }

            .nav-links li {
                margin: 0.5rem 1rem;
            }

            .rulebook-container {
                padding: 0 0.5rem;
            }

            table {
                font-size: 0.8rem;
            }

            .component-table {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="rulebook-nav">
        <div class="nav-container">
            <h1 class="nav-title">Om: The Journey</h1>
            <button class="menu-button" id="menuToggle">☰</button>
            <ul class="nav-links" id="navLinks">
                <li><a href="#01-introduction">Introduction</a></li>
                <li><a href="#02-components">Components</a></li>
                <li><a href="#03-setup">Setup</a></li>
                <li><a href="#04-gameplay">Gameplay</a></li>
                <li><a href="#05-om-turn-track">Om Turn Track</a></li>
                <li><a href="#06-end-game">End Game</a></li>
                <li><a href="#07-reference">Reference</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main content -->
    <div class="rulebook-container">
        <div class="cover-page">
    <h1>OM: THE JOURNEY</h1>
    <div class="subtitle">A Spiritual Adventure Across India</div>

    <div class="cover-image-placeholder">
        <!-- Placeholder: Beautiful illustration of Indian landscape with spiritual elements -->
        <img src="../output/assets/images/cover-art.jpg" alt="Om: The Journey Cover Art" class="game-image" />
    </div>

    <div class="game-info">
        <div class="players">
            <strong>2-3 Players</strong>
        </div>
        <div class="time">
            <strong>45-75 Minutes</strong>
        </div>
    </div>

    <div class="cover-credits">
        <p>A journey of outer exploration and inner transformation</p>
    </div>
</div> <section id="01-introduction" class="rulebook-section"><h1 class="section-title">Introduction & Theme</h1>
<p>In <strong>Om: The Journey</strong>, each player follows the path of a modern traveller exploring the vast lands of India—physically journeying across important sites (<strong>Outer Journey</strong>) while simultaneously cultivating spiritual growth (<strong>Inner Journey</strong>).</p>
<p>The board depicts six major regions—<strong>East</strong>, <strong>West</strong>, <strong>South</strong>,  <strong>North-East</strong>, <strong>North West</strong> and <strong>Central</strong>—each containing notable locations associated with either the Outer or Inner quest.</p>
<h2>Your Spiritual Adventure</h2>
<div class="callout-box">
Along your journey, you will:
<p>• <strong>Collect Energy Cubes</strong> of four types: Artha (wealth), Karma (action), Gnana (knowledge), and Bhakti (devotion)</p>
<p>• <strong>Acquire Journey Cards</strong> to mark progress on both your Outer and Inner tracks</p>
<p>• <strong>Earn Om Tokens</strong> from sacred Jyotirlinga locations, which influence turn order through the dynamic Om Turn Track</p>
</div>
<div class="board-diagram">
    <img src="../assets/images/board-overview.jpg" alt="Game Board Overview" class="game-image board-section" />
    <p class="text-center"><em>The six regions of India await your exploration</em></p>
</div>
<h2>Game Overview</h2>
<div class="sidebar-right">
    <div class="callout-box important">
        <h4>At a Glance</h4>
        <p><strong>Players:</strong> 2-3</p>
        <p><strong>Time:</strong> 45-75 minutes</p>
        <p><strong>Goal:</strong> First to 100+ combined points</p>
    </div>
</div>
<div class="main-content with-sidebar-right">
    <p>Your objective is simple yet profound: be the first player whose <strong>Outer Score + Inner Score exceeds 100 points</strong>. This triggers a final round, ensuring all players have equal turns.</p>

    <p>The game balances physical exploration with spiritual development, requiring strategic decisions about which path to prioritize while managing limited resources and adapting to changing conditions through Event cards.</p>

    <p>Each turn, you'll choose between acquiring travel cards to move across India or collecting Journey cards that advance your scoring tracks. The dynamic Om Turn Track adds tactical depth, as collecting Om Tokens from sacred sites influences turn order for future rounds.</p>
</div> </section><section id="02-components" class="rulebook-section"><h1 class="section-title">Components</h1>
<!-- <div class="component-table">
<table>
<thead>
<tr>
<th>Item</th>
<th>Quantity</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Main Game Board</strong></td>
<td>1</td>
<td>30&quot; × 22&quot; board depicting India's six regions, travel routes, and 12 Jyotirlinga spaces (each starts with 1 Om Token)</td>
</tr>
<tr>
<td><strong>Om Turn Track</strong></td>
<td>1</td>
<td>Printed on main board with numbered spaces 0-5 for dynamic turn order</td>
</tr>
<tr>
<td><strong>Player Markers</strong></td>
<td>12 total</td>
<td>4 meeples per player color: 1 traveller pawn, 2 score markers (Inner &amp; Outer tracks), 1 Om Turn track marker</td>
</tr>
<tr>
<td><strong>Energy Cubes</strong></td>
<td>48 total</td>
<td>12 each of Artha <span class="icon energy-cube artha"></span> (yellow), Karma <span class="icon energy-cube karma"></span> (green), Gnana <span class="icon energy-cube gnana"></span> (blue), Bhakti <span class="icon energy-cube bhakti"></span> (purple)</td>
</tr>
<tr>
<td><strong>Om Tokens</strong></td>
<td>12</td>
<td>Wooden tokens (0.5&quot;) placed on Jyotirlinga locations <span class="icon om-token">ॐ</span></td>
</tr>
<tr>
<td><strong>Character Cards</strong></td>
<td>8</td>
<td>Unique roles: Engineer, Professor, Merchant, Pilgrim</td>
</tr>
<tr>
<td><strong>Travel Cards</strong></td>
<td>24</td>
<td>Movement cards showing values 1-3 with vehicle types (Camel, Horse, Truck, Bus, Train, Helicopter, etc.)</td>
</tr>
<tr>
<td><strong>Journey Cards</strong></td>
<td>48 total</td>
<td>26 Outer Journey cards + 22 Inner Journey cards representing destinations and spiritual sites</td>
</tr>
<tr>
<td><strong>Event Cards</strong></td>
<td>30</td>
<td>Cards affecting all players each round with diverse thematic effects</td>
</tr>
<tr>
<td><strong>Scoring Tracks</strong></td>
<td>2</td>
<td>Outer Journey (0-100) and Inner Journey (0-100) printed on main board</td>
</tr>
</tbody>
</table>
</div> -->
<h2>Component Details</h2>
<div class="game-components">
    <!-- Placeholder: Beautiful illustration of Indian landscape with spiritual elements -->
    <img src="../output/assets/images/game-components.png" alt="Game Components" class="game-image" />
</div>
</section>
<div class="sidebar-left">
    <div class="callout-box setup">
        <h4>Quick Setup Check</h4>
        <p>Before your first game, verify you have:</p>
        <ul>
            <li>All 48 Energy Cubes (12 of each color)</li>
            <li>12 Om Tokens on Jyotirlinga spaces</li>
            <li>8 Character cards</li>
            <li>All player meeples (4 per color)</li>
        </ul>
    </div>
</div>
<div class="main-content with-sidebar-left">
<h3>Energy Cubes</h3>
<p>The four types of Energy Cubes represent different aspects of your journey:</p>
<ul>
<li><strong>Artha</strong> <span class="icon energy-cube artha"></span> (Yellow) - Material wealth and resources</li>
<li><strong>Karma</strong> <span class="icon energy-cube karma"></span> (Green) - Action and good deeds</li>
<li><strong>Gnana</strong> <span class="icon energy-cube gnana"></span> (Blue) - Knowledge and wisdom</li>
<li><strong>Bhakti</strong> <span class="icon energy-cube bhakti"></span> (Purple) - Devotion and spiritual practice</li>
</ul>
<p>These cubes serve as currency for acquiring Journey Cards and represent the balanced approach needed for both outer and inner development.</p>
<h3>Journey Cards</h3>
<p>Journey Cards are divided into two types:</p>
<p><strong>Outer Journey Cards</strong> focus on physical destinations, cultural sites, and worldly experiences. These require combinations of Artha and Karma cubes.</p>
<p><strong>Inner Journey Cards</strong> represent spiritual locations, temples, and places of worship. These require combinations of Gnana and Bhakti cubes.</p>
<h3>Event Cards</h3>
<p>Each round begins with a new Event that affects all players. These cards add thematic flavor and tactical variety, representing the changing conditions of travel in India - from monsoon delays to festival celebrations.</p>
</div>
<div class="page-break"></div>
<div class="component-spread">
    <!-- Component spread template will be inserted here -->
<!-- </div> <div class="component-spread">
    <div class="component-category">
        <h3>Board & Tracks</h3>
        <div class="component-grid">
            <div class="component-item">
                <div class="component-image placeholder">
                    Image
                </div>
                <div class="component-name">Main Game Board</div>
                <div class="component-desc">30" × 22" board showing India's six regions with routes and Jyotirlinga locations</div>
            </div>
            <div class="component-item">
                <div class="component-image placeholder">
                    Image
                </div>
                <div class="component-name">Om Turn Track</div>
                <div class="component-desc">Numbered spaces 0-5 for dynamic turn order</div>
            </div>
        </div>
    </div>

    <div class="component-category">
        <h3>Player Components</h3>
        <div class="component-grid">
            <div class="component-item">
                <div class="component-image">Player Meeples</div>
                <div class="component-name">Player Markers</div>
                <div class="component-desc">4 meeples per player: traveller, 2 score markers, Om track marker</div>
            </div>
            <div class="component-item">
                <div class="component-image">Character Cards</div>
                <div class="component-name">Character Cards</div>
                <div class="component-desc">4 unique roles: Engineer, Professor, Merchant, Pilgrim</div>
            </div>
        </div>
    </div>

    <div class="component-category">
        <h3>Energy & Tokens</h3>
        <div class="component-grid">
            <div class="component-item">
                <div class="component-image placeholder">
                    Image
                </div>
                <div class="component-name">Energy Cubes</div>
                <div class="component-desc">48 cubes total: 12 each of Artha (yellow), Karma (green), Gnana (blue), Bhakti (purple)</div>
            </div>
            <div class="component-item">
                <div class="component-image placeholder">
                    Image
                </div>
                <div class="component-name">Om Tokens</div>
                <div class="component-desc">12 wooden tokens placed on Jyotirlinga locations</div>
            </div>
        </div>
    </div>

    <div class="component-category">
        <h3>Travel Cards</h3>
        <div class="component-grid">
            <div class="component-item">
                <div class="component-image placeholder">
                    Image
                </div>
                <div class="component-name">Travel Cards</div>
                <div class="component-desc">24 cards showing movement values 1-3 with vehicle types</div>
            </div>
            <div class="component-item">
                <div class="component-image">Vehicle Icons</div>
                <div class="component-name">Vehicle Types</div>
                <div class="component-desc">Camel, Horse, Truck, Bus, Train, Helicopter, etc.</div>
            </div>
        </div>
    </div>

    <div class="component-category">
        <h3>Journey Cards</h3>
        <div class="component-grid">
            <div class="component-item">
                <div class="component-image placeholder">
                    Image
                </div>
                <div class="component-name">Outer Journey Cards</div>
                <div class="component-desc">26 cards representing physical destinations and cultural sites</div>
            </div>
            <div class="component-item">
                <div class="component-image placeholder">
                    Image
                </div>
                <div class="component-name">Inner Journey Cards</div>
                <div class="component-desc">22 cards representing spiritual locations and temples</div>
            </div>
        </div>
    </div>

    <div class="component-category">
        <h3>Event Cards</h3>
        <div class="component-grid">
            <div class="component-item">
                <div class="component-image placeholder">
                    Image
                </div>
                <div class="component-name">Event Cards</div>
                <div class="component-desc">30 cards affecting gameplay each round with diverse effects</div>
            </div>
            <div class="component-item">
                <div class="component-image">Examples</div>
                <div class="component-name">Event Types</div>
                <div class="component-desc">Movement restrictions, bonuses, special conditions, and thematic effects</div>
            </div>
        </div>
    </div>
</div>  -->
<section id="03-setup" class="rulebook-section"><h1 class="section-title">Setup</h1>
<p>Follow these steps to prepare for your journey across India.</p>
<h2>1. Board Setup</h2>
<div class="callout-box setup">
<h3>Om Tokens</h3>
<p>Place all 12 <strong>Om Tokens</strong> <span class="icon om-token">ॐ</span> on the Jyotirlinga locations marked on the board. These sacred sites are scattered across all six regions of India.</p>
</div>
<div class="board-diagram">
    <img src="../assets/images/setup-board.jpg" alt="Board Setup" class="game-image board-section" />
    <p class="text-center"><em>Initial board state with Om Tokens placed on all Jyotirlinga locations</em></p>
</div>
<h2>2. Energy Cubes Distribution</h2>
<div class="sidebar-right">
    <div class="callout-box important">
        <h4>Energy Cube Check</h4>
        <p>You should have exactly <strong>6 cubes</strong> remaining in the supply after placement:</p>
        <ul>
            <li>4 cubes (1 of each type) set aside initially</li>
            <li>2 extra cubes from random distribution</li>
        </ul>
        <p>If you have more than 6, check for missing placements on the board.</p>
    </div>
</div>
<div class="main-content with-sidebar-right">
<ol>
<li>
<p><strong>Reserve Supply</strong>: Set aside 1 Energy Cube of each color <span class="icon energy-cube artha"></span> <span class="icon energy-cube karma"></span> <span class="icon energy-cube gnana"></span> <span class="icon energy-cube bhakti"></span> in the cube tray.</p>
</li>
<li>
<p><strong>Random Distribution</strong>: Place all remaining cubes in the cup and shuffle thoroughly.</p>
</li>
<li>
<p><strong>Board Placement</strong>: Draw cubes one at a time and place them on locations throughout the board until all locations have cubes.</p>
</li>
<li>
<p><strong>Final Supply</strong>: Add the 2 remaining cubes to the 4 reserved cubes for a total supply of 6 cubes.</p>
</li>
</ol>
</div>
<h2>3. Player Setup</h2>
<div class="callout-box">
    <h3>Turn Order Determination</h3>
    <p><strong>If today's date ends in an even number</strong>: Youngest player goes first, followed clockwise.</p>
    <p><strong>If today's date ends in an odd number</strong>: Oldest player goes first, followed clockwise.</p>
    <p><strong>Om Turn Track</strong>: First player's marker goes on space 0 (top), second player under first player on space 0, third player under second player on space 0 (bottom).</p>
    </div>
<h3>Character Assignment</h3>
<div class="example-turn">
    <div class="turn-header">Character Selection</div>
    <div class="turn-step">Shuffle the 8 Character cards</div>
    <div class="turn-step">Each player chooses 1 card in turn order</div>
    <div class="turn-step">Return unused cards to the box</div>
    <div class="turn-step">Each player studies their character's special ability</div>
</div>
<h3>Player Pieces</h3>
<p>Each player chooses a color and takes:</p>
<ul>
<li>1 <strong>Traveller pawn</strong> (place on any of the 6 airport locations)</li>
<li>2 <strong>Score markers</strong> (place both on 0 of their respective tracks)</li>
<li>1 <strong>Om Turn Track marker</strong> (place according to turn order below)</li>
</ul>
<h2>4. Card Setup</h2>
<div class="sidebar-left">
    <div class="callout-box">
        <h4>Card Markets</h4>
        <p>Both Journey card markets should be visible to all players and easily accessible during the game.</p>
    </div>
</div>
<div class="main-content with-sidebar-left">
<h3>Journey Cards</h3>
<ol>
<li><strong>Separate</strong> Inner and Outer Journey cards (look for "I" or "O" markings)</li>
<li><strong>Shuffle</strong> each deck separately</li>
<li><strong>Deal 4 cards</strong> face-up from each deck to create the markets</li>
<li><strong>Place remaining decks</strong> nearby for refills</li>
</ol>
<h3>Travel Cards</h3>
<ul>
<li><strong>Shuffle</strong> the Travel deck</li>
<li><strong>Deal 4 cards</strong> face-up in 2 rows of 2 cards each</li>
<li><strong>Place the deck</strong> nearby for drawing from top</li>
</ul>
<h3>Event Cards</h3>
<ul>
<li><strong>Shuffle</strong> the Event deck</li>
<li><strong>Reveal the top card</strong> - this affects Round 1</li>
<li><strong>Place the deck</strong> nearby for future rounds</li>
</ul>
</div>
<h2>5. Final Setup Check</h2>
<div class="board-diagram">
    <img src="../assets/images/setup-complete.jpg" alt="Complete Setup" class="game-image board-section" />
    <p class="text-center"><em>Ready to begin - all components in starting positions</em></p>
</div>
<div class="callout-box important">
<h3>Before You Begin</h3>
<ul>
<li>[ ] All Om Tokens placed on Jyotirlinga locations</li>
<li>[ ] Energy Cubes distributed on board with 6 in supply</li>
<li>[ ] Each player has their 4 pieces placed correctly</li>
<li>[ ] Character cards dealt and abilities understood</li>
<li>[ ] Journey card markets (4 Inner + 4 Outer) face-up</li>
<li>[ ] Travel card market (4 cards) face-up</li>
<li>[ ] First Event card revealed</li>
<li>[ ] Turn order established on Om Turn Track</li>
</ul>
<p><strong>You're ready to begin your spiritual journey across India!</strong></p>
</div> </section><section id="04-gameplay" class="rulebook-section"><h1 class="section-title">Gameplay</h1>
<p>Each player's turn follows a structured sequence. The game continues until one player's combined Outer + Inner score exceeds 100 points, triggering the final round.</p>
<h2>Turn Sequence</h2>
<div class="example-turn">
    <div class="turn-header">Your Turn (in any order)</div>
    <div class="turn-step">1. Character Trading (Optional)</div>
    <div class="turn-step">2. Choose ONE Main Action</div>
    <div class="turn-step">3. Travelling (Optional)</div>
    <div class="turn-step">4. Collect Om Tokens (if applicable)</div>
    <div class="turn-step">5. Acquire Journey Card (if Action B chosen)</div>
    <div class="turn-step">6. End-of-Turn Cleanup</div>
</div>
<h2>1. Character Trading (Optional)</h2>
<div class="sidebar-left">
    <div class="callout-box">
        <h4>Character Abilities</h4>
        <p><strong>Engineer</strong> <span class="icon energy-cube karma"></span><br/>Trade any cube → 1 Karma</p>
        <p><strong>Professor</strong> <span class="icon energy-cube gnana"></span><br/>Trade any cube → 1 Gnana</p>
        <p><strong>Merchant</strong> <span class="icon energy-cube artha"></span><br/>Trade any cube → 1 Artha</p>
        <p><strong>Pilgrim</strong> <span class="icon energy-cube bhakti"></span><br/>Trade any cube → 1 Bhakti</p>
    </div>
</div>
<div class="main-content with-sidebar-left">
<p><strong>Once per turn</strong>, you may use your Character's ability to exchange Energy Cubes. Give any cube from your supply to the central pile and take 1 cube of your character's type.</p>
<p><strong>Important</strong>: You cannot trade a cube of the same type you receive (e.g., Merchant cannot trade Artha for Artha).</p>
<p>This ability helps you obtain the specific cube types needed for Journey Cards while managing your cube collection efficiently.</p>
</div>
<h2>2. Main Actions (Choose ONE)</h2>
<p>You must choose exactly one of these actions each turn:</p>
<h3>Action A: Acquire Travel Cards</h3>
<div class="callout-box">
<strong>Take any 2 Travel Cards</strong> from the market and/or deck in any combination:
<ul>
<li>Draw from the face-down deck</li>
<li>Take from the market (then refill empty market slots)</li>
<li>Combine both options</li>
</ul>
<p><strong>No cost required</strong> - Travel Cards are free to acquire.</p>
</div>
<h3>Action B: Acquire 1 Journey Card</h3>
<div class="callout-box important">
<strong>Choose and pay for 1 Journey Card</strong> from either market:
<ul>
<li><strong>Pay the Energy Cube cost</strong> (return cubes to central supply)</li>
<li><strong>Pay the Om Token cost</strong> based on your mat slot (1-1-2-3)</li>
<li><strong>Place the card</strong> in the next available slot of the correct track</li>
<li><strong>Advance your score marker</strong> by the card's point value</li>
</ul>
</div>
<div class="board-diagram">
    <img src="../assets/images/journey-card-examples.jpg" alt="Journey Card Examples" class="game-image" />
    <p class="text-center"><em>Examples of Outer and Inner Journey Cards with costs and rewards</em></p>
</div>
<h2>3. Travelling (Optional)</h2>
<div class="sidebar-right">
    <div class="callout-box setup">
        <h4>Movement Rules</h4>
        <ul>
            <li>Each card = exact number of hops</li>
            <li>Can't revisit locations during single card's movement</li>
            <li>Can chain multiple cards</li>
            <li>Movement is always optional</li>
            <li>Airports count as 1 hop between each other</li>
        </ul>
    </div>
</div>
<div class="main-content with-sidebar-right">
<p>Play any number of <strong>Travel Cards</strong> from your hand to move your traveller pawn across the board.</p>
<h3>Movement Mechanics</h3>
<ul>
<li><strong>Exact Hops</strong>: A card showing "3" requires exactly 3 steps along connected routes</li>
<li><strong>No Backtracking</strong>: Cannot revisit the same location during a single card's movement</li>
<li><strong>Chaining Cards</strong>: Play multiple cards in sequence (e.g., "2" + "1" = 3 total hops)</li>
<li><strong>Route Connections</strong>: Follow the printed routes between locations</li>
<li><strong>Airport Hopping</strong>: Moving from one airport to another counts as 1 hop</li>
</ul>
<h3>Example Movement</h3>
<div class="example-turn">
    <div class="turn-header">Maya's Movement</div>
    <div class="turn-step">Maya plays a "2" Travel Card showing a Train</div>
    <div class="turn-step">She moves 2 hops: Airport → Delhi → Agra</div>
    <div class="turn-step">Then plays a "1" card showing Bus</div>
    <div class="turn-step">She moves 1 more hop: Agra → Jaipur</div>
    <div class="turn-step">Total movement: 3 hops using 2 cards</div>
</div>
</div>
<h2>4. Collecting Om Tokens</h2>
<div class="callout-box important">
<strong>If you end movement on a Jyotirlinga location</strong> with an Om Token <span class="icon om-token">ॐ</span> <strong>and have space</strong> (max 3 tokens), immediately:
<ol>
<li><strong>Take the Om Token</strong> from the board</li>
<li><strong>Advance</strong> your pawn on the Om Turn Track one space per token collected this turn</li>
<li><strong>Stack</strong> on top if multiple pawns occupy the same space</li>
</ol>
</div>
<p><strong>Note</strong>: Om Tokens collected through Events also advance you on the Om Turn Track.</p>
<h2>5. Acquiring Journey Cards (Action B only)</h2>
<p>If you chose Action B this turn, now resolve the Journey Card acquisition:</p>
<div class="example-turn">
    <div class="turn-header">Journey Card Acquisition Example</div>
    <div class="turn-step">Raj wants the "Taj Mahal" Outer Journey Card (Cost: 2 Karma + 1 Artha, Worth: 27 points)</div>
    <div class="turn-step">He pays 2 <span class="icon energy-cube karma"></span> + 1 <span class="icon energy-cube artha"></span> to the supply</div>
    <div class="turn-step">This goes in his 3rd Outer slot, so he pays 2 <span class="icon om-token">ॐ</span></div>
    <div class="turn-step">He places the card and advances his Outer score by 27 points</div>
    <div class="turn-step">The market slot is refilled from the Outer Journey deck</div>
</div>
<h2>6. End-of-Turn Steps</h2>
<ul>
<li><strong>Refill</strong> any empty market slots (Journey Cards only)</li>
<li><strong>Pass play</strong> clockwise to the next player</li>
</ul>
<p>When all players have taken a turn, the <strong>round ends</strong>. Proceed to <strong>Om Turn Track &amp; Turn Order</strong> (next section).</p>
<div class="page-break"></div> </section><section id="05-om-turn-track" class="rulebook-section"><h1 class="section-title">Om Turn Track & Turn Order</h1>
<p>The <strong>Om Turn Track</strong> creates a dynamic turn order system that rewards strategic Om Token collection and adds tactical depth to your journey planning.</p>
<h2>How It Works</h2>
<div class="sidebar-right">
    <div class="callout-box">
        <h4>Key Points</h4>
        <ul>
            <li>Spaces 0-5 on the track</li>
            <li>Higher = earlier turn order</li>
            <li>Stack markers when tied</li>
            <li>Max advancement: space 5</li>
        </ul>
    </div>
</div>
<div class="main-content with-sidebar-right">
<p>At the end of each round, turn order for the next round is determined by reading the Om Turn Track from <strong>highest to lowest</strong>:</p>
<ol>
<li><strong>Highest pawn</strong> (topmost on tallest stack) becomes <strong>First Player</strong></li>
<li><strong>Second highest</strong> becomes Second Player</li>
<li><strong>Lowest</strong> becomes Last Player</li>
</ol>
<h3>Stacking Rules</h3>
<p>When multiple pawns occupy the same space, they <strong>stack vertically</strong>. The pawn that <strong>arrived most recently</strong> goes on <strong>top</strong> of the stack.</p>
</div>
<h2>Advancing on the Track</h2>
<div class="callout-box important">
<strong>When you collect Om Tokens</strong> <span class="icon om-token">ॐ</span>, <strong>immediately advance</strong> your pawn on the Om Turn Track:
<ul>
<li><strong>1 Om Token</strong> = advance 1 space</li>
<li><strong>2 Om Tokens</strong> = advance 2 spaces</li>
<li><strong>3 Om Tokens</strong> = advance 3 spaces</li>
</ul>
<p><strong>Maximum</strong>: Cannot advance beyond space 5, even with excess tokens.</p>
</div>
<h3>Sources of Om Tokens</h3>
<ul>
<li><strong>Jyotirlinga Visits</strong>: End movement on a Jyotirlinga location with a token</li>
<li><strong>Events</strong>: Some Event cards grant Om Tokens to players</li>
<li><strong>Both Sources</strong>: All Om Tokens collected in a turn advance you on the track</li>
</ul>
<h2>Turn Order Examples</h2>
<div class="example-turn">
    <div class="turn-header">Round 1 → Round 2 Turn Order</div>
    <div class="turn-step"><strong>Start of Round 1:</strong> Maya (space 0), Raj (space 1), Anil (space 2)</div>
    <div class="turn-step"><strong>During Round 1:</strong> Maya collects 2 Om Tokens → advances to space 2</div>
    <div class="turn-step"><strong>Raj collects 1 Om Token</strong> → advances to space 2</div>
    <div class="turn-step"><strong>End of Round 1:</strong> Maya and Raj both on space 2, Anil on space 2</div>
    <div class="turn-step"><strong>Stacking:</strong> Raj arrived at space 2 most recently, so goes on top</div>
    <div class="turn-step"><strong>Round 2 Order:</strong> Raj (1st), Maya (2nd), Anil (3rd)</div>
</div>
<div class="board-diagram">
    <img src="../assets/images/om-track-example.jpg" alt="Om Turn Track Example" class="game-image" />
    <p class="text-center"><em>Example showing pawn positions and stacking on the Om Turn Track</em></p>
</div>
<h2>Strategic Considerations</h2>
<div class="sidebar-left">
    <div class="callout-box setup">
        <h4>Tactical Tips</h4>
        <p><strong>Early Turns:</strong> Good for securing desired Journey Cards</p>
        <p><strong>Late Turns:</strong> Better reaction to Events and other players' moves</p>
        <p><strong>Denial:</strong> Sometimes leaving Om Tokens for others can be strategic</p>
    </div>
</div>
<div class="main-content with-sidebar-left">
<h3>When to Prioritize Om Tokens</h3>
<ul>
<li><strong>Powerful Events</strong>: Going first lets you capitalize on beneficial events</li>
<li><strong>Limited Journey Cards</strong>: Early access to high-value or needed cards</li>
<li><strong>Endgame Timing</strong>: Controlling when the final round triggers</li>
</ul>
<h3>When to Hold Back</h3>
<ul>
<li><strong>Restrictive Events</strong>: Sometimes going later is advantageous</li>
<li><strong>Resource Planning</strong>: Other players' actions might create better opportunities</li>
<li><strong>Psychological Factor</strong>: Appearing less threatening can be valuable</li>
</ul>
<p>The Om Turn Track transforms what could be a static player order into a dynamic, strategic element that's deeply integrated with the game's spiritual theme of collecting sacred tokens.</p>
</div> </section><section id="06-end-game" class="rulebook-section"><h1 class="section-title">End Game</h1>
<p>The game ends when any player's combined Outer + Inner score first exceeds 100 points, triggering a final round to ensure all players have equal turns.</p>

<h2>Game End Trigger</h2>
<div class="callout-box important">
<p><strong>Victory Condition:</strong> The first player whose <strong>Outer Score + Inner Score exceeds 100 points</strong> triggers the end game.</p>
<p>This can happen immediately when acquiring a Journey Card or through Event card bonuses.</p>
</div>

<h2>Final Round</h2>
<div class="sidebar-right">
    <div class="callout-box">
        <h4>Final Round Rules</h4>
        <ul>
            <li>All players get equal turns</li>
            <li>Normal gameplay rules apply</li>
            <li>New Event revealed if needed</li>
            <li>Continue until round completes</li>
        </ul>
    </div>
</div>

<div class="main-content with-sidebar-right">
<p><strong>Once the end game is triggered</strong>, continue play until every player has taken one more turn. This ensures fairness regardless of turn order.</p>

<h3>Event Cards in Final Round</h3>
<p>If the player who triggered the final round was the <strong>last player</strong> in turn order, reveal a new Event card for the final round. Otherwise, continue with the current Event.</p>

<h3>Final Round Example</h3>
<div class="example-turn">
    <div class="turn-header">End Game Scenario</div>
    <div class="turn-step">Maya (2nd player) reaches 103 points during Round 5</div>
    <div class="turn-step">Game end is triggered immediately</div>
    <div class="turn-step">Raj (3rd player) completes his turn normally</div>
    <div class="turn-step">Round 6 begins with current Event still active</div>
    <div class="turn-step">All players take one final turn in normal order</div>
    <div class="turn-step">Game ends after final turn, scores are compared</div>
</div>
</div>

<h2>Determining the Winner</h2>
<div class="callout-box important">
<h3>Victory Determination</h3>
<p><strong>Winner:</strong> Player with the <strong>highest combined Outer + Inner score</strong> after the final round.</p>
</div>

<h3>Tie Resolution</h3>
<p>If multiple players have the same combined score:</p>
<ol>
<li><strong>First Tiebreaker:</strong> Compare individual track scores (this shouldn't happen with the scoring system, but check anyway)</li>
<li><strong>Second Tiebreaker:</strong> Check the <strong>Om Turn Track</strong> - the player whose pawn is on the <strong>higher-numbered space</strong> wins</li>
<li><strong>Third Tiebreaker:</strong> If tied players' pawns are on the <strong>same space</strong>, they <strong>share victory</strong></li>
</ol>

<div class="example-turn">
    <div class="turn-header">Tie Resolution Example</div>
    <div class="turn-step">Final scores: Maya 108, Raj 108, Anil 105</div>
    <div class="turn-step">Maya and Raj are tied for first place</div>
    <div class="turn-step">Check Om Turn Track: Maya on space 3, Raj on space 2</div>
    <div class="turn-step">Maya wins due to higher position on Om Turn Track</div>
</div>

<h2>Strategic Endgame Considerations</h2>
<div class="sidebar-left">
    <div class="callout-box setup">
        <h4>Endgame Tips</h4>
        <ul>
            <li>Monitor all players' scores closely</li>
            <li>Plan for potential tie situations</li>
            <li>Om Turn Track position matters for ties</li>
            <li>Final round gives everyone a last chance</li>
        </ul>
    </div>
</div>

<div class="main-content with-sidebar-left">
<h3>Timing the End Game</h3>
<p>Players in leading positions should consider:</p>
<ul>
<li><strong>Turn Order:</strong> Earlier turn order allows first chance at reaching 100+ points</li>
<li><strong>Om Turn Track:</strong> Higher positions provide tiebreaker advantage</li>
<li><strong>Opponent Tracking:</strong> Monitor other players' potential to reach 100+ points</li>
</ul>

<h3>Catching Up</h3>
<p>Players behind should focus on:</p>
<ul>
<li><strong>High-Value Cards:</strong> Journey Cards worth 27-35 points can close gaps quickly</li>
<li><strong>Event Bonuses:</strong> Some Events provide significant point bonuses</li>
<li><strong>Balanced Scoring:</strong> Both Outer and Inner points count equally</li>
</ul>

<p>The end game maintains tension until the final turn, as the final round gives all players one last opportunity to maximize their spiritual journey's rewards.</p>
</div>

<div class="page-break"></div>
</section><section id="07-reference" class="rulebook-section"><h1 class="section-title">Reference Material</h1>
<p>This section provides quick reference information for all game components.</p>
<h2>Character Abilities</h2>
<div class="reference-card">
    <div class="card-title">Engineer</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube karma"></span> Karma</div>
    <p>Specializes in action and construction projects. Cannot trade Karma for Karma.</p>
</div>
<div class="reference-card">
    <div class="card-title">Professor</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube gnana"></span> Gnana</div>
    <p>Master of knowledge and wisdom. Cannot trade Gnana for Gnana.</p>
</div>
<div class="reference-card">
    <div class="card-title">Merchant</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube artha"></span> Artha</div>
    <p>Expert in commerce and material wealth. Cannot trade Artha for Artha.</p>
</div>
<div class="reference-card">
    <div class="card-title">Pilgrim</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube bhakti"></span> Bhakti</div>
    <p>Devoted spiritual seeker. Cannot trade Bhakti for Bhakti.</p>
</div>
<div class="page-break"></div>
<h2>Event Cards</h2>
<h3>Movement &amp; Travel Events</h3>
<div class="component-table">
<table>
<thead>
<tr>
<th>Event</th>
<th>Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Drizzle of Delay</strong></td>
<td>Max 2 moves; ending in North or East costs 1 <span class="icon energy-cube artha"></span></td>
</tr>
<tr>
<td><strong>Turbulent Skies</strong></td>
<td>No airport travel allowed this round</td>
</tr>
<tr>
<td><strong>Frozen North</strong></td>
<td>Starting in North: moves cost ×2; if moved, +7 Inner points</td>
</tr>
<tr>
<td><strong>Excess Baggage</strong></td>
<td>Hand limit 2; discard down immediately</td>
</tr>
</tbody>
</table>
</div>
<h3>Scoring &amp; Bonus Events</h3>
<div class="component-table">
<table>
<thead>
<tr>
<th>Event</th>
<th>Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Diwali Distraction</strong></td>
<td>All gain +5 Inner points but no cube pickup</td>
</tr>
<tr>
<td><strong>Maha Kumbh</strong></td>
<td>Visit any Jyotirlinga for 7 Inner points; skip for 1 bonus cube</td>
</tr>
<tr>
<td><strong>Bountiful Bhandara</strong></td>
<td>Draw 2 random cubes; +5 Outer points if any Journey Card collected</td>
</tr>
<tr>
<td><strong>Triathlon</strong></td>
<td>+7 Outer points if three different Travel card values used</td>
</tr>
</tbody>
</table>
</div>
<h3>Character-Specific Events</h3>
<div class="component-table">
<table>
<thead>
<tr>
<th>Event</th>
<th>Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Merchant's Midas</strong></td>
<td>Merchant trade for Artha: +7 Outer points</td>
</tr>
<tr>
<td><strong>Professor's Insight</strong></td>
<td>Professor trade for Gnana: +7 Inner points</td>
</tr>
<tr>
<td><strong>Pilgrim's Grace</strong></td>
<td>Pilgrim trade for Bhakti: +7 Inner points</td>
</tr>
<tr>
<td><strong>Engineer's Precision</strong></td>
<td>Engineer trade for Karma: +7 Outer points</td>
</tr>
</tbody>
</table>
</div>
<h3>Vehicle-Specific Events</h3>
<div class="component-table">
<table>
<thead>
<tr>
<th>Event</th>
<th>Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Eco Trail</strong></td>
<td>Cycle or Trek: +5 Inner points</td>
</tr>
<tr>
<td><strong>Rajput Caravans</strong></td>
<td>Horse or Camel: +5 Outer points</td>
</tr>
<tr>
<td><strong>Urban Ride</strong></td>
<td>Motorbike or Rickshaw: +5 Outer points</td>
</tr>
<tr>
<td><strong>Road Warriors</strong></td>
<td>Car or Bus: +5 Outer points</td>
</tr>
<tr>
<td><strong>Rails and Sails</strong></td>
<td>Train or Boat: +5 Outer points</td>
</tr>
<tr>
<td><strong>Heavy Haul</strong></td>
<td>Use Truck for 10 Outer points but lose 2 cubes</td>
</tr>
</tbody>
</table>
</div>

<h2>Quick Reference Summary</h2>
<div class="callout-box important">
<h3>Turn Sequence</h3>
<ol>
<li>Character Trading (optional)</li>
<li>Main Action (A: Travel Cards OR B: Journey Card)</li>
<li>Travelling (optional)</li>
<li>Collect Om Tokens (if applicable)</li>
<li>Acquire Journey Card (if Action B)</li>
<li>End-of-Turn cleanup</li>
</ol>
<h3>Victory Condition</h3>
<p>First player to exceed <strong>100 combined points</strong> (Outer + Inner) triggers final round.</p>
<h3>Om Token Collection</h3>
<ul>
<li>Max 3 tokens per player</li>
<li>Advance 1 space per token on Om Turn Track</li>
<li>Collected from Jyotirlingas and some Events</li>
</ul>
</div> </section>
    </div>

    <!-- Footer with icon legend -->
    <footer class="rulebook-footer">
        <div class="icon-legend">
            <h3>Icon Reference</h3>
            <div class="legend-row">
                <div class="legend-item">
                    <div class="icon energy-cube artha"></div>
                    <span>Artha (Wealth)</span>
                </div>
                <div class="legend-item">
                    <div class="icon energy-cube karma"></div>
                    <span>Karma (Action)</span>
                </div>
                <div class="legend-item">
                    <div class="icon energy-cube gnana"></div>
                    <span>Gnana (Knowledge)</span>
                </div>
                <div class="legend-item">
                    <div class="icon energy-cube bhakti"></div>
                    <span>Bhakti (Devotion)</span>
                </div>
                <div class="legend-item">
                    <div class="icon om-token">ॐ</div>
                    <span>Om Token</span>
                </div>
            </div>
        </div>
        <a href="#" class="back-to-top">Back to Top</a>
    </footer>

    <!-- JavaScript for responsive navigation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            const navLinks = document.getElementById('navLinks');

            menuToggle.addEventListener('click', function() {
                navLinks.classList.toggle('show');
            });

            // Close menu when clicking a link
            const links = navLinks.querySelectorAll('a');
            links.forEach(link => {
                link.addEventListener('click', function() {
                    navLinks.classList.remove('show');
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.rulebook-nav')) {
                    navLinks.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>